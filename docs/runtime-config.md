# 运行时配置系统

## 概述

本项目支持运行时配置，允许在构建完成后通过修改配置文件来改变应用行为，无需重新编译。

## 配置优先级

1. **运行时配置文件** (`/config.js`) - 最高优先级
2. **环境变量** (`.env.*` 文件) - 中等优先级  
3. **默认值** - 最低优先级

## 文件结构

```
public/
├── config.js                 # 生产环境运行时配置
├── config.development.js     # 开发环境配置模板
├── config.test.js           # 测试环境配置模板
└── ...

src/config/
├── runtimeConfig.js         # 运行时配置管理器
├── index.js                # 主配置文件
└── thirdPartyConfig.js     # 第三方系统配置

scripts/
└── deploy-config.js        # 部署配置管理脚本
```

## 使用方法

### 1. 开发阶段

开发时仍然使用环境变量文件 (`.env.development`, `.env.test`, `.env.production`)：

```bash
# 开发环境
npm run dev

# 构建不同环境
npm run build:dev    # 开发环境构建
npm run build:test   # 测试环境构建  
npm run build:prod   # 生产环境构建
```

### 2. 部署阶段

构建完成后，可以通过以下方式修改配置：

#### 方法一：使用部署脚本

```bash
# 切换到开发环境配置
npm run deploy:config:dev

# 切换到测试环境配置
npm run deploy:config:test

# 切换到生产环境配置
npm run deploy:config:prod
```

#### 方法二：直接修改配置文件

直接编辑 `dist/config.js` 文件：

```javascript
window.RUNTIME_CONFIG = {
  app: {
    title: '你的医院管理系统',
    version: '2.0.0',
    env: 'production'
  },
  api: {
    baseURL: 'https://your-api-server.com',
    timeout: 15000
  },
  thirdParty: {
    yingchunhua: {
      sdkUrl: 'https://your-sdk-url.com/sdk.js',
      appKey: 'your-app-key',
      appSecretKey: 'your-secret-key'
    }
  }
}
```

### 3. 配置项说明

#### 应用配置 (app)
- `title`: 应用标题
- `version`: 应用版本
- `env`: 环境标识 (development/test/production)

#### API配置 (api)
- `baseURL`: API基础地址
- `timeout`: 请求超时时间 (毫秒)
- `version`: API版本

#### 第三方系统配置 (thirdParty)
- `yingchunhua`: 迎春花质控系统配置
  - `sdkUrl`: SDK地址
  - `appKey`: 应用密钥
  - `appSecretKey`: 应用私钥
  - `linkType`: 链接类型 (1-app, 2-web)
  - `qualityTarget`: 质控目标 (1-临床, 2-病理)

- `drg`: DRG系统配置
  - `apiUrl`: DRG API地址
  - `appKey`: DRG应用密钥
  - `appSecret`: DRG应用私钥

#### 功能开关 (features)
- `enableDebugMode`: 是否启用调试模式
- `enablePerformanceMonitoring`: 是否启用性能监控
- `enableErrorReporting`: 是否启用错误上报

#### 其他配置 (other)
- `logLevel`: 日志级别 (debug/info/warn/error)
- `enableConsoleLog`: 是否启用控制台日志

## 部署流程

### 标准部署流程

1. **构建应用**
   ```bash
   npm run build:prod
   ```

2. **部署到服务器**
   ```bash
   # 将 dist 目录部署到服务器
   ```

3. **根据环境修改配置**
   ```bash
   # 在服务器上直接编辑 config.js
   vi /path/to/your/app/config.js
   ```

4. **重启Web服务器**（可选）
   ```bash
   # 某些服务器可能需要重启以清除缓存
   ```

### Docker部署

```dockerfile
# Dockerfile 示例
FROM nginx:alpine

# 复制构建产物
COPY dist/ /usr/share/nginx/html/

# 复制配置文件模板
COPY public/config.*.js /usr/share/nginx/html/

# 启动脚本可以根据环境变量选择配置文件
COPY docker-entrypoint.sh /docker-entrypoint.sh
RUN chmod +x /docker-entrypoint.sh

ENTRYPOINT ["/docker-entrypoint.sh"]
```

```bash
# docker-entrypoint.sh 示例
#!/bin/sh

# 根据环境变量选择配置文件
ENV=${DEPLOY_ENV:-production}

if [ -f "/usr/share/nginx/html/config.${ENV}.js" ]; then
    cp "/usr/share/nginx/html/config.${ENV}.js" "/usr/share/nginx/html/config.js"
    echo "使用 ${ENV} 环境配置"
else
    echo "配置文件 config.${ENV}.js 不存在，使用默认配置"
fi

# 启动nginx
nginx -g 'daemon off;'
```

## 注意事项

1. **缓存问题**: 修改配置文件后，浏览器可能会缓存旧的配置文件，建议：
   - 清除浏览器缓存
   - 或在配置文件URL后添加版本参数

2. **安全性**: 
   - 不要在运行时配置中存储敏感信息
   - 生产环境的配置文件应该通过安全的方式管理

3. **兼容性**: 
   - 如果运行时配置文件加载失败，系统会自动回退到环境变量配置
   - 确保关键配置项在环境变量中也有备份

4. **调试**: 
   - 可以通过浏览器控制台查看当前使用的配置
   - 开发环境会显示详细的配置加载日志

## 故障排除

### 配置不生效
1. 检查 `config.js` 文件是否存在且语法正确
2. 检查浏览器控制台是否有错误信息
3. 清除浏览器缓存后重试

### 配置文件加载失败
1. 检查文件路径是否正确
2. 检查Web服务器是否正确配置静态文件服务
3. 查看网络请求是否成功

### 部分配置不生效
1. 检查配置项名称是否正确
2. 检查配置值的数据类型是否正确
3. 查看控制台日志确认配置加载情况

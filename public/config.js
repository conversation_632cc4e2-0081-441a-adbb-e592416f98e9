/**
 * 运行时配置文件
 * 此文件可以在构建后修改，无需重新编译
 * 部署时可以根据不同环境修改此文件的内容
 */
window.RUNTIME_CONFIG = {
  // 应用基础配置
  app: {
    title: '测试医院管理系统',
    version: '1.0.0',
    env: 'production'
  },

  // API配置
  api: {
    baseURL: 'https://api.yyhospital.com',
    timeout: 15000,
    version: 'v1'
  },

  // 第三方系统配置
  thirdParty: {
    // 迎春花质控系统
    yingchunhua: {
      sdkUrl: 'http://183.242.68.188:8094/client_app_iframe/index.js',
      appKey: 'ajNLOHF3dERoMmhob1FNSi9ZTFpJUT09',
      appSecretKey: 'YYCloud1644286723584',
      linkType: '2',
      qualityTarget: '2'
    },

    // DRG系统配置
    drg: {
      apiUrl: '',
      appKey: '',
      appSecret: ''
    }
  },

  // 功能开关
  features: {
    enableDebugMode: false,
    enablePerformanceMonitoring: true,
    enableErrorReporting: true
  },

  // 其他配置
  other: {
    logLevel: 'warn', // debug, info, warn, error
    enableConsoleLog: false
  }
}

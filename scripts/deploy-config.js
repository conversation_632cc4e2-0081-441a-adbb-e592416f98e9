/**
 * 部署配置管理脚本
 * 用于在部署时切换不同环境的运行时配置
 */

import { readFileSync, writeFileSync, existsSync } from 'fs'
import { resolve } from 'path'
import { fileURLToPath } from 'url'
import { dirname } from 'path'

const __filename = fileURLToPath(import.meta.url)
const __dirname = dirname(__filename)
const root = resolve(__dirname, '..')

// 获取命令行参数
const args = process.argv.slice(2)
const environment = args[0] || 'production'

console.log('🚀 部署配置管理')
console.log('=' .repeat(50))
console.log(`📋 目标环境: ${environment}`)

// 配置文件路径
const configFiles = {
  development: resolve(root, 'public/config.development.js'),
  test: resolve(root, 'public/config.test.js'),
  production: resolve(root, 'public/config.js')
}

const targetConfigPath = resolve(root, 'dist/config.js')
const sourceConfigPath = configFiles[environment]

// 检查源配置文件是否存在
if (!existsSync(sourceConfigPath)) {
  console.error(`❌ 配置文件不存在: ${sourceConfigPath}`)
  console.log('💡 可用的环境配置:')
  Object.keys(configFiles).forEach(env => {
    const exists = existsSync(configFiles[env])
    console.log(`  - ${env}: ${exists ? '✅' : '❌'}`)
  })
  process.exit(1)
}

try {
  // 读取源配置文件
  const configContent = readFileSync(sourceConfigPath, 'utf8')
  
  // 写入目标位置
  writeFileSync(targetConfigPath, configContent)
  
  console.log(`✅ 配置文件已复制`)
  console.log(`📁 源文件: ${sourceConfigPath}`)
  console.log(`📁 目标文件: ${targetConfigPath}`)
  
  // 显示配置内容摘要
  console.log('\n📋 配置摘要:')
  
  // 简单解析配置内容（提取关键信息）
  const lines = configContent.split('\n')
  const configLines = lines.filter(line => 
    line.includes(':') && 
    !line.trim().startsWith('//') && 
    !line.trim().startsWith('/*')
  )
  
  configLines.slice(0, 10).forEach(line => {
    const trimmed = line.trim()
    if (trimmed && !trimmed.includes('{') && !trimmed.includes('}')) {
      console.log(`  ${trimmed}`)
    }
  })
  
  if (configLines.length > 10) {
    console.log(`  ... 还有 ${configLines.length - 10} 项配置`)
  }
  
} catch (error) {
  console.error('❌ 配置文件复制失败:', error.message)
  process.exit(1)
}

console.log('\n💡 使用说明:')
console.log('  🔧 开发环境: npm run deploy:config development')
console.log('  🧪 测试环境: npm run deploy:config test')
console.log('  🚀 生产环境: npm run deploy:config production')

console.log('\n📝 手动修改配置:')
console.log(`  直接编辑 dist/config.js 文件即可`)
console.log(`  修改后刷新页面即可生效，无需重新构建`)

console.log('=' .repeat(50))

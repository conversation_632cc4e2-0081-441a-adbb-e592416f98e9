/**
 * 运行时配置管理器
 * 支持编译时环境变量和运行时配置文件的混合使用
 */

// 获取编译时环境变量
const env = import.meta.env

/**
 * 获取运行时配置
 * 优先级：运行时配置 > 环境变量 > 默认值
 */
const getRuntimeConfig = () => {
  // 如果运行时配置存在，使用运行时配置
  if (window.RUNTIME_CONFIG) {
    return window.RUNTIME_CONFIG
  }

  // 否则使用环境变量构建配置
  return {
    app: {
      title: env.VITE_APP_TITLE || '测试医院管理系统',
      version: env.VITE_APP_VERSION || '1.0.0',
      env: env.VITE_APP_ENV || 'development'
    },
    api: {
      baseURL: env.VITE_API_BASE_URL || (env.DEV ? '/api' : 'http://localhost:6596'),
      timeout: parseInt(env.VITE_API_TIMEOUT) || 10000,
      version: 'v1'
    },
    thirdParty: {
      yingchunhua: {
        sdkUrl: env.VITE_YINGCHUNHUA_SDK_URL || 'http://**************:8094/client_app_iframe/index.js',
        appKey: env.VITE_YINGCHUNHUA_APP_KEY || '',
        appSecretKey: env.VITE_YINGCHUNHUA_APP_SECRET_KEY || '',
        linkType: env.VITE_YINGCHUNHUA_LINK_TYPE || '2',
        qualityTarget: env.VITE_YINGCHUNHUA_QUALITY_TARGET || '2'
      },
      drg: {
        apiUrl: env.VITE_DRG_API_URL || '',
        appKey: env.VITE_DRG_APP_KEY || '',
        appSecret: env.VITE_DRG_APP_SECRET || ''
      }
    },
    features: {
      enableDebugMode: env.DEV || false,
      enablePerformanceMonitoring: true,
      enableErrorReporting: !env.DEV
    },
    other: {
      logLevel: env.DEV ? 'debug' : 'warn',
      enableConsoleLog: env.DEV || false
    }
  }
}

/**
 * 配置管理器类
 */
class ConfigManager {
  constructor() {
    this.config = null
    this.initialized = false
  }

  /**
   * 初始化配置
   */
  async init() {
    if (this.initialized) {
      return this.config
    }

    try {
      // 尝试加载运行时配置
      await this.loadRuntimeConfig()
      this.config = getRuntimeConfig()
      this.initialized = true
      
      console.log('🔧 配置管理器初始化完成')
      console.log('📋 当前配置来源:', window.RUNTIME_CONFIG ? '运行时配置文件' : '环境变量')
      
      return this.config
    } catch (error) {
      console.warn('⚠️ 运行时配置加载失败，使用环境变量配置:', error.message)
      this.config = getRuntimeConfig()
      this.initialized = true
      return this.config
    }
  }

  /**
   * 动态加载运行时配置文件
   */
  loadRuntimeConfig() {
    return new Promise((resolve, reject) => {
      // 如果已经存在运行时配置，直接返回
      if (window.RUNTIME_CONFIG) {
        resolve()
        return
      }

      // 动态加载配置文件
      const script = document.createElement('script')
      script.src = '/config.js?t=' + Date.now() // 添加时间戳防止缓存
      script.onload = () => {
        console.log('✅ 运行时配置文件加载成功')
        resolve()
      }
      script.onerror = () => {
        reject(new Error('运行时配置文件加载失败'))
      }
      document.head.appendChild(script)
    })
  }

  /**
   * 获取配置值
   */
  get(path, defaultValue = null) {
    if (!this.initialized) {
      console.warn('⚠️ 配置管理器未初始化，请先调用 init() 方法')
      return defaultValue
    }

    const keys = path.split('.')
    let value = this.config

    for (const key of keys) {
      if (value && typeof value === 'object' && key in value) {
        value = value[key]
      } else {
        return defaultValue
      }
    }

    return value
  }

  /**
   * 获取完整配置
   */
  getAll() {
    return this.config
  }

  /**
   * 检查是否为开发环境
   */
  isDev() {
    return this.get('app.env') === 'development' || env.DEV
  }

  /**
   * 检查是否为生产环境
   */
  isProd() {
    return this.get('app.env') === 'production'
  }

  /**
   * 检查是否为测试环境
   */
  isTest() {
    return this.get('app.env') === 'test'
  }
}

// 创建全局配置管理器实例
const configManager = new ConfigManager()

// 导出配置管理器和便捷方法
export default configManager

/**
 * 便捷的配置获取方法
 */
export const getConfig = (path, defaultValue) => {
  return configManager.get(path, defaultValue)
}

/**
 * 获取API配置
 */
export const getApiConfig = () => {
  return configManager.get('api', {})
}

/**
 * 获取第三方系统配置
 */
export const getThirdPartyConfig = (system) => {
  return configManager.get(`thirdParty.${system}`, {})
}

/**
 * 获取应用配置
 */
export const getAppConfig = () => {
  return configManager.get('app', {})
}

/**
 * 应用配置管理
 * 统一管理环境变量和应用配置
 * 支持运行时配置和编译时环境变量
 */

import configManager, { getConfig, getApiConfig, getAppConfig } from './runtimeConfig.js'

// 获取环境变量（用于兼容性）
const env = import.meta.env

/**
 * 初始化配置（异步）
 * 在应用启动时调用
 */
export const initConfig = async () => {
  await configManager.init()
  return configManager.getAll()
}

/**
 * 应用基础配置（动态获取）
 */
export const appConfig = {
  get title() { return getConfig('app.title', '测试医院管理系统') },
  get version() { return getConfig('app.version', '1.0.0') },
  get env() { return getConfig('app.env', 'development') },

  // 开发模式判断
  get isDev() { return configManager.isDev() },
  get isProd() { return configManager.isProd() },
  get isTest() { return configManager.isTest() }
}

/**
 * API配置（动态获取）
 */
export const apiConfig = {
  // 基础URL
  get baseURL() {
    const config = getApiConfig()
    return config.baseURL || (env.DEV ? '/api' : 'http://localhost:6596')
  },

  // 后端服务器地址（仅用于参考）
  get backendURL() {
    return getConfig('api.backendURL') || env.VITE_BACKEND_URL || 'http://localhost:6596'
  },

  // 请求超时时间
  get timeout() {
    return getConfig('api.timeout', 10000)
  },

  // API版本
  get version() {
    return getConfig('api.version', 'v1')
  },

  // 请求头配置
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json'
  },

  // 是否使用代理
  get useProxy() {
    return env.DEV && this.baseURL === '/api'
  }
}

/**
 * 获取完整的API地址
 * @param {string} endpoint - API端点
 * @returns {string} 完整的API地址
 */
export const getApiUrl = (endpoint) => {
  // 移除端点开头的斜杠（如果有的话）
  const cleanEndpoint = endpoint.startsWith('/') ? endpoint.slice(1) : endpoint
  return `${apiConfig.baseURL}/${cleanEndpoint}`
}

/**
 * 日志配置
 */
export const logConfig = {
  // 是否启用日志
  enabled: appConfig.isDev,
  
  // 日志级别
  level: appConfig.isDev ? 'debug' : 'error'
}

// 导出默认配置
export default {
  app: appConfig,
  api: apiConfig,
  log: logConfig,
  getApiUrl
}

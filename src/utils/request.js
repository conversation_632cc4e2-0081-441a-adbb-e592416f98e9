/**
 * HTTP请求封装
 * 基于axios的统一请求客户端
 */

import axios from 'axios'
import { apiConfig } from '@/config'
import { getConfig } from '@/config/runtimeConfig'

/**
 * 创建axios实例
 */
const request = axios.create({
  baseURL: apiConfig.baseURL,
  timeout: apiConfig.timeout,
  headers: apiConfig.headers,
  // 开发环境下的额外配置
  ...(apiConfig.useProxy && {
    // 使用代理时的特殊配置
    withCredentials: false, // 代理环境下通常不需要携带凭证
  })
})

// 打印当前API配置信息
if (logConfig.enabled) {
  console.group('🔧 HTTP客户端配置')
  console.log('📡 Base URL:', apiConfig.baseURL)
  console.log('🌐 Backend URL:', apiConfig.backendURL)
  console.log('🔄 使用代理:', apiConfig.useProxy)
  console.log('⏱️ 超时时间:', apiConfig.timeout + 'ms')
  console.groupEnd()
}

/**
 * 请求拦截器
 */
request.interceptors.request.use(
  (config) => {
    // 请求开始时间和唯一ID（用于计算请求耗时和追踪重复请求）
    const requestId = Math.random().toString(36).substr(2, 9)
    config.metadata = {
      startTime: new Date(),
      requestId: requestId
    }


    // 可以在这里添加token等认证信息
    // const token = localStorage.getItem('token')
    // if (token) {
    //   config.headers.Authorization = `Bearer ${token}`
    // }

    return config
  },
  (error) => {
    console.error('❌ Request Error:', error)
    return Promise.reject(error)
  }
)

/**
 * 响应拦截器
 */
request.interceptors.response.use(
  (response) => {
    // 计算请求耗时
    const endTime = new Date()
    const duration = endTime - response.config.metadata.startTime
    const requestId = response.config.metadata.requestId


    
    // 统一处理响应数据
    const { data } = response
    
    // 根据后端API规范处理响应
    if (data && typeof data === 'object') {
      // 如果响应包含code字段，按照标准格式处理
      if ('code' in data) {
        if (data.code === 0) {
          // 成功响应
          return data
        } else {
          // 业务错误
          const error = new Error(data.msg || '请求失败')
          error.code = data.code
          error.data = data
          throw error
        }
      }
    }
    
    // 直接返回响应数据
    return data
  },
  (error) => {
    // 计算请求耗时（如果有的话）
    let duration = 'unknown'
    if (error.config?.metadata?.startTime) {
      const endTime = new Date()
      duration = `${endTime - error.config.metadata.startTime}ms`
    }
    
    console.error(`❌ API Error: ${error.config?.method?.toUpperCase()} ${error.config?.url} (${duration})`, error)
    
    // 统一错误处理
    let errorMessage = '网络请求失败'
    
    if (error.response) {
      // 服务器响应了错误状态码
      const { status, data } = error.response
      
      switch (status) {
        case 400:
          errorMessage = data?.msg || '请求参数错误'
          break
        case 401:
          errorMessage = '未授权，请重新登录'
          // 可以在这里处理登录跳转
          break
        case 403:
          errorMessage = '拒绝访问'
          break
        case 404:
          errorMessage = '请求的资源不存在'
          break
        case 500:
          errorMessage = '服务器内部错误'
          break
        case 502:
          errorMessage = '网关错误'
          break
        case 503:
          errorMessage = '服务不可用'
          break
        default:
          errorMessage = data?.msg || `请求失败 (${status})`
      }
    } else if (error.request) {
      // 请求已发出但没有收到响应
      if (error.code === 'ECONNABORTED') {
        errorMessage = '请求超时，请稍后重试'
      } else if (error.code === 'ERR_NETWORK') {
        errorMessage = '网络连接失败，请检查网络设置'
      } else {
        errorMessage = '网络请求失败，请检查网络连接'
      }
    }
    
    // 创建统一的错误对象
    const apiError = new Error(errorMessage)
    apiError.originalError = error
    apiError.status = error.response?.status
    apiError.code = error.code
    
    return Promise.reject(apiError)
  }
)

export default request

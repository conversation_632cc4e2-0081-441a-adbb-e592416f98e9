# 本地环境变量配置示例
# 复制此文件为 .env.local 并填入实际的配置值
# .env.local 文件不会被提交到版本控制系统

# 迎春花质控系统配置
# 注意：这些是敏感信息，请妥善保管
# 如果需要覆盖默认配置，请取消注释并填入实际值

# SDK地址（可选，如果不设置将使用默认值）
# VITE_YINGCHUNHUA_SDK_URL=your_sdk_url_here

# 应用密钥（可选，如果不设置将使用默认值）
# VITE_YINGCHUNHUA_APP_KEY=your_app_key_here

# 应用密钥（必填，请联系项目经理获取）
VITE_YINGCHUNHUA_APP_SECRET_KEY=your_secret_key_here

# DRG系统配置（如果需要）
# VITE_DRG_API_URL=your_drg_api_url_here
# VITE_DRG_APP_KEY=your_drg_app_key_here
# VITE_DRG_APP_SECRET=your_drg_app_secret_here

# 其他本地配置
# VITE_BACKEND_URL=http://your-local-backend:port
